<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.StatisticsInAgainInitMapper">

	<!-- 表名 -->
	<sql id="tableName">
        statistics_in_again_init
    </sql>
    
    <sql id="columns">
        `id`,`statistic_date`,`status`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != statisticDate and '' != statisticDate">
            AND `statistic_date` = #{statisticDate}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

</mapper>

