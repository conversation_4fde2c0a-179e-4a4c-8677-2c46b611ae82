<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.OutOrderItemMapper">
    <!-- 查询主工单id -->
    <select id="getItem" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        select *
        from out_order_item_${param.outPassageCode}
        where correlate_id = #{param.correlateId}
    </select>
</mapper>

