<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.statistics.mapper.bms.BmsProgramMapper">


    <select id="selectIdListByBmsContentId" resultType="java.lang.Long">
        SELECT
            bp.id
        FROM
            `bms_program` bp
                LEFT JOIN bms_content bc ON bp.cms_series_id = bc.cms_content_id
                AND bp.sp_id = bc.sp_id
        WHERE
            bc.id = #{bmsContentId}
    </select>
    <select id="selectIdsByCondition" resultType="java.lang.Long">
        SELECT
            bp.id
        FROM
            `bms_program` bp
                LEFT JOIN bms_content bc ON bp.cms_series_id = bc.cms_content_id
                AND bp.sp_id = bc.sp_id
        WHERE
            bc.id = #{bmsContentId}
    </select>
</mapper>