<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.StatisticsDiskSpaceMapper">

    <!-- 表名 -->
    <sql id="tableName">
        statistics_disk_space
    </sql>

    <sql id="columns">
        `id`
        ,`total_space`,`used_space`,`used_proportion`,`storage_id`,`storage_name`,`cp_id`,`cp_name`,`statistic_date`,`create_time`,`update_time`
    </sql>

    <sql id="where">
        <where>
            <if test="null != id">
                AND `id` = #{id}
            </if>
            <if test="null != totalSpace">
                AND `total_space` = #{totalSpace}
            </if>
            <if test="null != usedSpace">
                AND `used_space` = #{usedSpace}
            </if>
            <if test="null != usedProportion and '' != usedProportion">
                AND `used_proportion` = #{usedProportion}
            </if>
            <if test="null != storageId and '' != storageId">
                AND `storage_id` = #{storageId}
            </if>
            <if test="null != storageName and '' != storageName">
                AND `storage_name` = #{storageName}
            </if>
            <if test="null != cpId">
                AND `cp_id` = #{cpId}
            </if>
            <if test="null != cpName and '' != cpName">
                AND `cp_name` = #{cpName}
            </if>
            <if test="null != statisticDate and '' != statisticDate">
                AND `statistic_date` = #{statisticDate}
            </if>
            <if test="null != createTime and '' != createTime">
                AND `create_time` = #{createTime}
            </if>
            <if test="null != updateTime and '' != updateTime">
                AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

    <insert id="batchInsert">
        insert into
        statistics_disk_space(`total_space`,`used_space`,`used_proportion`,`cp_id`,`cp_name`,`storage_id`,`storage_name`,`statistic_date`)
        VALUES
        <foreach item="item" index="index" collection="statisticsDiskSpaces" separator=",">
            (#{item.totalSpace},#{item.usedSpace},#{item.usedProportion},#{item.cpId},#{item.cpName},#{item.storageId},#{item.storageName},#{item.statisticDate})
        </foreach>
    </insert>

    <select id="selectDiskSpaceOverThreshold" resultType="java.lang.String">
        SELECT sds.cp_name
        FROM `statistics_disk_space` sds
        LEFT JOIN sys_cp sc ON sds.cp_id = sc.id
        WHERE sds.used_proportion >= sc.space_threshold
        AND sc.id IN
        <foreach collection="cpIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND sds.statistic_date BETWEEN (DATE_FORMAT(CURDATE(),'%Y-%m-%d %H:%i:%s'))
        AND NOW()
    </select>

    <select id="selectDiskSpaceTotal" resultType="com.pukka.iptv.statistics.model.StatisticsDiskSpace">
        SELECT total_space,
               SUM(used_space)          AS used_space,
               used_space / total_space AS used_proportion
        FROM statistics_disk_space
        WHERE (storage_id = #{storageId} AND statistic_date BETWEEN #{beginOfDay} AND #{endOfDay})
    </select>

    <select id="getDiskSpacePieChart" resultType="com.pukka.iptv.statistics.vo.StatisticsDiskSpaceCpRespVo">
        SELECT
        sds.cp_id,
        sds.cp_name,
        sds.used_space,
        sds.total_space,
        sds.used_proportion,
        sc.space_threshold,
        IFNULL(
        sds.used_space / ( SELECT used_space FROM statistics_disk_space WHERE ( storage_id = #{storageId} AND
        statistic_date BETWEEN #{beginOfDay} AND #{endOfDay} AND cp_id IS NULL ) ) ,0.0000)AS used_proportion_total
        FROM
        statistics_disk_space sds
        LEFT JOIN sys_cp sc ON sds.cp_id = sc.id
        WHERE
        ( sds.storage_id = #{storageId}
        <if test="null != cpId and '' != cpId">
            AND `cp_id` = #{cpId}
        </if>
        AND sds.statistic_date BETWEEN #{beginOfDay} AND #{endOfDay} )
    </select>
    <select id="selectDiskSpaceStop" resultType="java.lang.String">
        SELECT sds.cp_name
        FROM `statistics_disk_space` sds
        LEFT JOIN sys_cp sc ON sds.cp_id = sc.id
        WHERE sds.used_proportion >=
        IF
        (sc.space_threshold >= 0, 0.9, sc.space_threshold)
        AND sc.id IN
        <foreach collection="cpIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND sds.statistic_date BETWEEN (DATE_FORMAT(CURDATE(),'%Y-%m-%d %H:%i:%s'))
        AND NOW()
    </select>
</mapper>

