<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.StatisticsPublishMapper">

	<!-- 表名 -->
	<sql id="tableName">
        statistics_publish
    </sql>
    
    <sql id="columns">
        `id`,`content_type`,`count`,`type`,`work_order_type`,`cp_id`,`cp_name`,`sp_id`,`sp_name`,`bms_sp_channel_name`,
`bms_sp_channel_id`,`bms_sp_channel_code`,`statistic_date`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != count">
            AND `count` = #{count}
            </if>
	        <if test="null != workOrderType">
            AND `work_order_type` = #{workOrderType}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != bmsSpChannelName and '' != bmsSpChannelName">
            AND `bms_sp_channel_name` = #{bmsSpChannelName}
            </if>
	        <if test="null != bmsSpChannelId">
            AND `bms_sp_channel_id` = #{bmsSpChannelId}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

    <insert id="batchInsert">
        insert into
        statistics_publish(`content_type`,`count`,`type`,`cp_id`,`cp_name`,`sp_id`,`sp_name`,
                              `bms_sp_channel_name`,`bms_sp_channel_id`,`bms_sp_channel_code`,`statistic_date`)
        VALUES
        <foreach item="item" index="index" collection="statisticsPublishes" separator=",">
            (#{item.contentType},#{item.count},#{item.type},#{item.cpId},#{item.cpName},#{item.spId},#{item.spName},
             #{item.bmsSpChannelName},#{item.bmsSpChannelId},#{item.bmsSpChannelCode},#{item.statisticDate})
        </foreach>
    </insert>
</mapper>

