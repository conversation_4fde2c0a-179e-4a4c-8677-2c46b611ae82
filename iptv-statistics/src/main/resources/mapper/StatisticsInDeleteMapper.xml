<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.StatisticsInDeleteMapper">

	<!-- 表名 -->
	<sql id="tableName">
        statistics_in_delete
    </sql>
    
    <sql id="columns">
        `id`,`type`,`content_type`,`code`,`name`,`cms_content_id`,`cms_content_code`,`statictic_date`,`status`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != cmsContentId">
            AND `cms_content_id` = #{cmsContentId}
            </if>
	        <if test="null != cmsContentCode and '' != cmsContentCode">
            AND `cms_content_code` = #{cmsContentCode}
            </if>
	        <if test="null != staticticDate and '' != staticticDate">
            AND `statictic_date` = #{staticticDate}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

</mapper>

