<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.sys.SysStorageMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_storage
    </sql>
    
    <sql id="columns">
        `id`,`name`,`storage_type`,`outer_url`,`inner_url`,`picture_url`,`status`,`original_directory`,`movie_directory`,`picture_directory`,`document_directory`,`cmd_directory`,`in_picture_prefix`,`in_movie_prefix`,`in_xml_prefix`,`out_picture_prefix`,`out_movie_prefix`,`out_xml_prefix`,`out_movie_http_prefix`,`out_picture_http_prefix`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != storageType">
            AND `storage_type` = #{storageType}
            </if>
	        <if test="null != outerUrl and '' != outerUrl">
            AND `outer_url` = #{outerUrl}
            </if>
	        <if test="null != innerUrl and '' != innerUrl">
            AND `inner_url` = #{innerUrl}
            </if>
	        <if test="null != pictureUrl and '' != pictureUrl">
            AND `picture_url` = #{pictureUrl}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != originalDirectory and '' != originalDirectory">
            AND `original_directory` = #{originalDirectory}
            </if>
	        <if test="null != movieDirectory and '' != movieDirectory">
            AND `movie_directory` = #{movieDirectory}
            </if>
	        <if test="null != pictureDirectory and '' != pictureDirectory">
            AND `picture_directory` = #{pictureDirectory}
            </if>
	        <if test="null != documentDirectory and '' != documentDirectoryr">
            AND `document_directory` = #{documentDirectory}
            </if>
	        <if test="null != cmdDirectory and '' != cmdDirectory">
            AND `cmd_directory` = #{cmdDirectory}
            </if>
	        <if test="null != inPicturePrefix and '' != inPicturePrefix">
            AND `in_picture_prefix` = #{inPicturePrefix}
            </if>
	        <if test="null != inMoviePrefix and '' != inMoviePrefix">
            AND `in_movie_prefix` = #{inMoviePrefix}
            </if>
	        <if test="null != inXmlPrefix and '' != inXmlPrefix">
            AND `in_xml_prefix` = #{inXmlPrefix}
            </if>
	        <if test="null != outPicturePrefix and '' != outPicturePrefix">
            AND `out_picture_prefix` = #{outPicturePrefix}
            </if>
	        <if test="null != outMoviePrefix and '' != outMoviePrefix">
            AND `out_movie_prefix` = #{outMoviePrefix}
            </if>
	        <if test="null != outXmlPrefix and '' != outXmlPrefix">
            AND `out_xml_prefix` = #{outXmlPrefix}
            </if>
        </where>
    </sql>

</mapper>

