<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.statistics.mapper.bms.BmsContentMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsContent" id="bmsContentMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="orderNumber" column="order_number"/>
        <result property="originalName" column="original_name"/>
        <result property="licensingWindowStart" column="licensing_window_start"/>
        <result property="licensingWindowEnd" column="licensing_window_end"/>
        <result property="displayAsNew" column="display_as_new"/>
        <result property="displayAsLastChance" column="display_as_last_chance"/>
        <result property="pgmCategoryId" column="pgm_category_id"/>
        <result property="pgmCategory" column="pgm_category"/>
        <result property="pgmSndClassId" column="pgm_snd_class_id"/>
        <result property="pgmSndClass" column="pgm_snd_class"/>
        <result property="status" column="status"/>
        <result property="contentProvider" column="content_provider"/>
        <result property="cpId" column="cp_id"/>
        <result property="cpName" column="cp_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="contentType" column="content_type"/>
        <result property="cpCheckStatus" column="cp_check_status"/>
        <result property="cpCheckDesc" column="cp_check_desc"/>
        <result property="cpCheckTime" column="cp_check_time"/>
        <result property="cpChecker" column="cp_checker"/>
        <result property="opCheckStatus" column="op_check_status"/>
        <result property="opCheckDesc" column="op_check_desc"/>
        <result property="opChecker" column="op_checker"/>
        <result property="opCheckTime" column="op_check_time"/>
        <result property="source" column="source"/>
        <result property="lockStatus" column="lock_status"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="timedPublish" column="timed_publish"/>
        <result property="timedPublishStatus" column="timed_publish_status"/>
        <result property="timedPublishDescription" column="timed_publish_description"/>
        <result property="cmsContentCode" column="cms_content_code"/>
        <result property="cmsContentId" column="cms_content_id"/>
        <result property="missedInfo" column="missed_info"/>
        <result property="cpFeedbackFlag" column="cp_feedback_flag"/>
        <result property="definitionFlag" column="definition_flag"/>
        <result property="duration" column="duration"/>
        <result property="firstPublishTime" column="first_publish_time"/>
    </resultMap>


</mapper>