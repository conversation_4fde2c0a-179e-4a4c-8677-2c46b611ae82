<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.cms">

	<!-- 表名 -->
	<sql id="tableName">
        cms_series
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`order_number`,`original_name`,`sort_name`,`search_name`,`org_air_date`,`licensing_window_start`,`licensing_window_end`,`display_as_new`,`display_as_last_chance`,`macrovision`,`price`,`volumn_count`,`volumn_update`,`status`,`description`,`kpeople`,`director`,`actor_display`,`script_writer`,`writer_display`,`compere`,`guest`,`reporter`,`op_incharge`,`series_type`,`copy_right`,`content_provider`,`pgm_category_id`,`pgm_category`,`pgm_snd_class_id`,`pgm_snd_class`,`vsp_code`,`rating`,`cucc_price`,`ctcc_price`,`cmcc_price`,`cp_id`,`cp_name`,`create_time`,`update_time`,`cp_check_status`,`cp_check_desc`,`cp_check_time`,`cp_checker`,`op_check_status`,`op_check_desc`,`op_checker`,`op_check_time`,`source`,`creator_name`,`creator_id`,`publisher`,`approval`,`release_year`,`definition_flag`,`lock_status`,`original_country_id`,`original_country`,`movie_head_duration`,`movie_tail_duration`,`missed_info`,`missing_detection`,`type`,`tags`
    </sql>

    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != orderNumber and '' != orderNumber">
            AND `order_number` = #{orderNumber}
            </if>
	        <if test="null != originalName and '' != originalName">
            AND `original_name` = #{originalName}
            </if>
	        <if test="null != sortName and '' != sortName">
            AND `sort_name` = #{sortName}
            </if>
	        <if test="null != searchName and '' != searchName">
            AND `search_name` = #{searchName}
            </if>
	        <if test="null != orgAirDate and '' != orgAirDate">
            AND `org_air_date` = #{orgAirDate}
            </if>
	        <if test="null != licensingWindowStart and '' != licensingWindowStart">
            AND `licensing_window_start` = #{licensingWindowStart}
            </if>
	        <if test="null != licensingWindowEnd and '' != licensingWindowEnd">
            AND `licensing_window_end` = #{licensingWindowEnd}
            </if>
	        <if test="null != displayAsNew">
            AND `display_as_new` = #{displayAsNew}
            </if>
	        <if test="null != displayAsLastChance">
            AND `display_as_last_chance` = #{displayAsLastChance}
            </if>
	        <if test="null != macrovision">
            AND `macrovision` = #{macrovision}
            </if>
	        <if test="null != price and '' != price">
            AND `price` = #{price}
            </if>
	        <if test="null != volumnCount">
            AND `volumn_count` = #{volumnCount}
            </if>
	        <if test="null != volumnUpdate">
            AND `volumn_update` = #{volumnUpdate}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != kpeople and '' != kpeople">
            AND `kpeople` = #{kpeople}
            </if>
	        <if test="null != director and '' != director">
            AND `director` = #{director}
            </if>
	        <if test="null != actorDisplay and '' != actorDisplay">
            AND `actor_display` = #{actorDisplay}
            </if>
	        <if test="null != scriptWriter and '' != scriptWriter">
            AND `script_writer` = #{scriptWriter}
            </if>
	        <if test="null != writerDisplay and '' != writerDisplay">
            AND `writer_display` = #{writerDisplay}
            </if>
	        <if test="null != compere and '' != compere">
            AND `compere` = #{compere}
            </if>
	        <if test="null != guest and '' != guest">
            AND `guest` = #{guest}
            </if>
	        <if test="null != reporter and '' != reporter">
            AND `reporter` = #{reporter}
            </if>
	        <if test="null != opIncharge and '' != opIncharge">
            AND `op_incharge` = #{opIncharge}
            </if>
	        <if test="null != seriesType">
            AND `series_type` = #{seriesType}
            </if>
	        <if test="null != copyRight and '' != copyRight">
            AND `copy_right` = #{copyRight}
            </if>
	        <if test="null != contentProvider and '' != contentProvider">
            AND `content_provider` = #{contentProvider}
            </if>
	        <if test="null != pgmCategoryId">
            AND `pgm_category_id` = #{pgmCategoryId}
            </if>
	        <if test="null != pgmCategory and '' != pgmCategory">
            AND `pgm_category` = #{pgmCategory}
            </if>
	        <if test="null != pgmSndClassId">
            AND `pgm_snd_class_id` = #{pgmSndClassId}
            </if>
	        <if test="null != pgmSndClass and '' != pgmSndClass">
            AND `pgm_snd_class` = #{pgmSndClass}
            </if>
	        <if test="null != vspCode and '' != vspCode">
            AND `vsp_code` = #{vspCode}
            </if>
	        <if test="null != rating and '' != rating">
            AND `rating` = #{rating}
            </if>
	        <if test="null != cuccPrice and '' != cuccPrice">
            AND `cucc_price` = #{cuccPrice}
            </if>
	        <if test="null != ctccPrice and '' != ctccPrice">
            AND `ctcc_price` = #{ctccPrice}
            </if>
	        <if test="null != cmccPrice and '' != cmccPrice">
            AND `cmcc_price` = #{cmccPrice}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != cpCheckStatus">
            AND `cp_check_status` = #{cpCheckStatus}
            </if>
	        <if test="null != cpCheckDesc and '' != cpCheckDesc">
            AND `cp_check_desc` = #{cpCheckDesc}
            </if>
	        <if test="null != cpCheckTime and '' != cpCheckTime">
            AND `cp_check_time` = #{cpCheckTime}
            </if>
	        <if test="null != cpChecker and '' != cpChecker">
            AND `cp_checker` = #{cpChecker}
            </if>
	        <if test="null != opCheckStatus">
            AND `op_check_status` = #{opCheckStatus}
            </if>
	        <if test="null != opCheckDesc and '' != opCheckDesc">
            AND `op_check_desc` = #{opCheckDesc}
            </if>
	        <if test="null != opChecker and '' != opChecker">
            AND `op_checker` = #{opChecker}
            </if>
	        <if test="null != opCheckTime and '' != opCheckTime">
            AND `op_check_time` = #{opCheckTime}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != publisher and '' != publisher">
            AND `publisher` = #{publisher}
            </if>
	        <if test="null != approval and '' != approval">
            AND `approval` = #{approval}
            </if>
	        <if test="null != releaseYear and '' != releaseYear">
            AND `release_year` = #{releaseYear}
            </if>
	        <if test="null != definitionFlag and '' != definitionFlag">
            AND `definition_flag` = #{definitionFlag}
            </if>
	        <if test="null != editLockStatus">
            AND `edit_lock_status` = #{editLockStatus}
            </if>
	        <if test="null != originalCountryId">
            AND `original_country_id` = #{originalCountryId}
            </if>
	        <if test="null != originalCountryName and '' != originalCountryName">
            AND `original_country_name` = #{originalCountryName}
            </if>
	        <if test="null != movieHeadDuration">
            AND `movie_head_duration` = #{movieHeadDuration}
            </if>
	        <if test="null != movieTailDuration">
            AND `movie_tail_duration` = #{movieTailDuration}
            </if>
	        <if test="null != missedInfo and '' != missedInfo">
            AND `missed_info` = #{missedInfo}
            </if>
	        <if test="null != missingDetection">
            AND `missing_detection` = #{missingDetection}
            </if>
        </where>
    </sql>


</mapper>

