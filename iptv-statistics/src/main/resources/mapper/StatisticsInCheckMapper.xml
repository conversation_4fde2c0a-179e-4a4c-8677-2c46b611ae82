<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.StatisticsInCheckMapper">

	<!-- 表名 -->
	<sql id="tableName">
        statistics_in_check
    </sql>
    
    <sql id="columns">
        `id`,`type`,`content_type`,`count`,`duration`,`cp_id`,`cp_name`,`statistic_date`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != count">
            AND `count` = #{count}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != statisticDate and '' != statisticDate">
            AND `statistic_date` = #{statisticDate}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

    <select id="getSelfAllStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">

        select type, sum(count) as count, sum(duration) as duration
        from statistics_in_check
        where 1=1
        <if test="param.cpId != null">
            AND cp_id = #{param.cpId}
        </if>
        <if test="param.contentType != null">
            AND content_type = #{param.contentType}
        </if>
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        group by type
    </select>

    <select id="getSelfAllStatisticsByAuth"   resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">

        select type, sum(count) as count, sum(duration) as duration
        from statistics_in_check
        where 1=1
        <if test="param.cpId != null">
            AND cp_id = #{param.cpId}
        </if>
        <if test="param.contentType != null">
            AND content_type = #{param.contentType}
        </if>
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        <if test="cpIdList != null">
            <trim prefix="AND cp_id IN">
                <foreach collection="cpIdList" index="index" item="cpId" separator="," open="(" close=")">
                    #{cpId}
                </foreach>
            </trim>
        </if>
        group by type
    </select>

    <select id="getSelfAllStatisticsLastMonth"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">

        select statistic_date as statisticDate, type, sum(count) as count, sum(duration) as duration
        from statistics_in_check
        where 1=1
        <if test="param.cpId != null">
            AND cp_id = #{param.cpId}
        </if>
        <if test="param.contentType != null">
            AND content_type = #{param.contentType}
        </if>
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        group by statistic_date, type
    </select>

    <select id="getSelfAllStatisticsLastMonthByAuth"    resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">

        select statistic_date as statisticDate, type, sum(count) as count, sum(duration) as duration
        from statistics_in_check
        where 1=1
        <if test="param.cpId != null">
            AND cp_id = #{param.cpId}
        </if>
        <if test="param.contentType != null">
            AND content_type = #{param.contentType}
        </if>
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(statistic_date, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        <if test="cpIdList != null">
            <trim prefix="AND cp_id IN">
                <foreach collection="cpIdList" index="index" item="cpId" separator="," open="(" close=")">
                    #{cpId}
                </foreach>
            </trim>
        </if>
        group by statistic_date, type
    </select>

</mapper>

