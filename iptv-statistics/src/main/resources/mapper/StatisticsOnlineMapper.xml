<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.statistics.mapper.StatisticsOnlineMapper">

	<!-- 表名 -->
	<sql id="tableName">
        statistics_online
    </sql>
    
    <sql id="columns">
        `id`,`content_type`,`count`,`duration`,`file_size`,`media_online_status`,`definition_flag`,`media_bind_status`,`cp_id`,`cp_name`,`sp_id`,`sp_name`,`bms_sp_channel_name`,`bms_sp_channel_id`,`statistic_date`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != count">
            AND `count` = #{count}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != fileSize">
            AND `file_size` = #{fileSize}
            </if>
	        <if test="null != mediaOnlineStatus">
            AND `media_online_status` = #{mediaOnlineStatus}
            </if>
	        <if test="null != definitionFlag">
            AND `definition_flag` = #{definitionFlag}
            </if>
	        <if test="null != mediaBindStatus">
            AND `media_bind_status` = #{mediaBindStatus}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != bmsSpChannelName and '' != bmsSpChannelName">
            AND `bms_sp_channel_name` = #{bmsSpChannelName}
            </if>
	        <if test="null != bmsSpChannelId">
            AND `bms_sp_channel_id` = #{bmsSpChannelId}
            </if>
            <if test="null != statisticDate and '' != statisticDate">
                AND `statistic_date` = #{statisticDate}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

</mapper>

