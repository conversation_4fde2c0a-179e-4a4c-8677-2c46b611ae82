<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.statistics.mapper.bms.BmsCategoryContentMapper">

    <select id="selectListMy" resultType="com.pukka.iptv.common.data.model.bms.BmsCategoryContent">
        SELECT
            bms_content_id,
            min( publish_time ) AS publish_time
        FROM
            bms_category_content bcc
        WHERE
            bcc.publish_status IN ( 3, 5, 6, 7 )
          AND bcc.content_type IN (3,4)
          AND EXISTS ( SELECT 1 FROM bms_content bc WHERE bc.id = bcc.bms_content_id )
        GROUP BY
            bms_content_id
    </select>

    <select id="selectListContentMy" resultType="com.pukka.iptv.common.data.model.bms.BmsCategoryContent">
        SELECT
            bms_content_id,
            min( publish_time ) AS publish_time
        FROM
            bms_category_content bcc
        WHERE
            bcc.publish_status IN ( 3, 5, 6, 7 )
          AND EXISTS ( SELECT 1 FROM bms_content bc WHERE bc.id = bcc.bms_content_id )
        GROUP BY
            bms_content_id
    </select>

    <select id="selectProgramListMy" resultType="java.util.Map">
        SELECT
            bc.id,group_concat(bp.id) ids
        FROM
            `bms_program` bp
                INNER JOIN bms_content bc ON bc.cms_content_id = bp.cms_series_id
                AND bp.sp_id = bc.sp_id
                AND bc.content_type in (3,4)
        WHERE
            bc.id in
        <foreach collection="bmsContentIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY bc.id;
    </select>
    <select id="selectNewRelationPublishTime" resultType="java.lang.String">
        SELECT
            min( publish_time ) AS publish_time
        FROM
            bms_category_content
        WHERE
            bms_content_id = #{bmsContentId}
          AND publish_status = 3
    </select>


</mapper>