${AnsiColor.BRIGHT_GREEN}
Application Version: ${project.version}
Spring Boot Version: ${spring-boot.version}
Spring Application Name: ${spring.application.name}
              ___                   ___                           ___
            ,--.'|_               ,--.'|_    ,--,               ,--.'|_    ,--,
            |  | :,'              |  | :,' ,--.'|               |  | :,' ,--.'|
  .--.--.   :  : ' :              :  : ' : |  |,      .--.--.   :  : ' : |  |,                .--.--.
 /  /    '.;__,'  /    ,--.--.  .;__,'  /  `--'_     /  /    '.;__,'  /  `--'_       ,---.   /  /    '
|  :  /`./|  |   |    /       \ |  |   |   ,' ,'|   |  :  /`./|  |   |   ,' ,'|     /     \ |  :  /`./
|  :  ;_  :__,'| :   .--.  .-. |:__,'| :   '  | |   |  :  ;_  :__,'| :   '  | |    /    / ' |  :  ;_
 \  \    `. '  : |__  \__\/: . .  '  : |__ |  | :    \  \    `. '  : |__ |  | :   .    ' /   \  \    `.
  `----.   \|  | '.'| ," .--.; |  |  | '.'|'  : |__   `----.   \|  | '.'|'  : |__ '   ; :__   `----.   \
 /  /`--'  /;  :    ;/  /  ,.  |  ;  :    ;|  | '.'| /  /`--'  /;  :    ;|  | '.'|'   | '.'| /  /`--'  /
'--'.     / |  ,   /;  :   .'   \ |  ,   / ;  :    ;'--'.     / |  ,   / ;  :    ;|   :    :'--'.     /
  `--'---'   ---`-' |  ,     .-./  ---`-'  |  ,   /   `--'---'   ---`-'  |  ,   /  \   \  /   `--'---'
                     `--`---'               ---`-'                        ---`-'    `----'