# spring config
spring:
  application:
    name: @project.artifactId@
    deleteBeforeInit: true
  profiles:
    active: @profiles.active@
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      allow-override: true     # 允许nacos被本地文件覆盖
      override-none: true     # nacos不覆盖任何本地文件
      override-system-properties: true   # nacos 覆盖系统属性。注意本地配置文件不是系统属性
    nacos:
      discovery:
        metadata:
          management:
            context-path: ${server.servlet.context-path:}/actuator
        #            health.path: /health
        server-addr: ${nacos.server-addr}
        username: ${nacos.username}
        password: ${nacos.password}
        #cluster-name: iptv-dev
        namespace: ${nacos.namespace}
        group: ${nacos.group}
        enabled: true
      config:
        server-addr: ${nacos.server-addr}
        username: ${nacos.username}
        password: ${nacos.password}
        #cluster-name: iptv-dev
        file-extension: ${nacos.file-extension}
        namespace: ${nacos.namespace}
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
        #共享配置
        shared-configs:
          - data-id: iptv-rabbitmq-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-mysql-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-white-list-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-config-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-priority-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-es-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${nacos.group}
            refresh: true
# log config
log:
  file:
    path: /iptv/bokong/data/applogs/${spring.application.name}/${hostname}/
    name: info
    suffix: log
    compress:
      suffix: gz
logging:
  file:
    name: ${log.file.path}${log.file.name}.${log.file.suffix}
  pattern:
    file: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
  level:
    com.pukka.iptv.statistics.mapper: debug
#mybatis:
#    configuration:
#      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
# seata config
#seata:
#  enabled: true
#  enable-auto-data-source-proxy: true #是否开启数据源自动代理,默认为true
#  application-id: ${spring.application.name}
#  tx-service-group: iptv-cloud-tx-service-group  #要与配置文件中的vgroupMapping一致
#  registry:  #registry根据seata服务端的registry配置
#    type: nacos #默认为file
#    nacos:
#      application: seata-server #配置自己的seata服务
#      server-addr: ${nacos.server-addr} #根据自己的seata服务配置
#      username: ${nacos.username} #根据自己的seata服务配置
#      password: ${nacos.password} #根据自己的seata服务配置
#      namespace: ${nacos.namespace} #根据自己的seata服务配置
#      cluster: default # 配置自己的seata服务cluster, 默认为 default
#      group: ${nacos.group} #根据自己的seata服务配置
#  config:
#    type: nacos #默认file,如果使用file不配置下面的nacos,直接配置seata.service
#    nacos:
#      server-addr: ${nacos.server-addr} #配置自己的nacos地址
#      group: ${nacos.group} #配置自己的dev
#      username: ${nacos.username} #配置自己的username
#      password: ${nacos.password} #配置自己的password
#      namespace: ${nacos.namespace} #配置自己的namespace
#      dataId: iptv-seata.properties #配置自己的dataId,由于搭建服务端时把客户端的配置也写在了seataServer.properties,所以这里用了和服务端一样的配置文件,实际客户端和服务端的配置文件分离出来更好
