package com.pukka.iptv.schedule.rules.service.listener;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.rabbitmq.constans.FeedbackConstant;
import com.pukka.iptv.common.rabbitmq.constans.ScheduleRulesConstant;
import com.pukka.iptv.schedule.rules.service.ScheduleRulesApplication;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ScheduleRulesApplication.class)
class RabbitMQListenerIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(RabbitMQListenerIntegrationTest.class);

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @BeforeEach
    void setup() {
        // 设置消息转换器为 JSON
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
    }

    @AfterEach
    void tearDown() {
        // 清理工作，如删除队列中的消息等
    }

    @Test
    void testFeedBackAutoReceived_SuccessScenario() throws Exception {
        // Given
        PublishParamsDto params = new PublishParamsDto();
        params.setIsFinish(true);
        params.setContentType(ContentTypeEnum.SCHEDULE.getValue());
        params.setCorrelateId("0000020120202312126546513024561");

        // When
        rabbitTemplate.convertAndSend(
                FeedbackConstant.FEEDBACK_EXCHANGE,
                ScheduleRulesConstant.SCHEDULE_RULES_AUTO_PUBLISH_ROUTING,
                params
        );

        // Sleep to give listener time to process message
        Thread.sleep(2000);

        // Then
        // 这里可以验证数据库状态，或者其他业务状态来确认处理成功。
        log.info("成功场景：消息已发送到队列并处理。");
    }

    @Test
    void testFeedBackAutoReceived_FailureScenario() throws Exception {
        // Given
        PublishParamsDto params = new PublishParamsDto();
        params.setIsFinish(true);
        params.setContentType(ContentTypeEnum.PACKAGE.getValue());
        params.setCorrelateId(""); // 无效的内容类型，模拟处理失败

        // When
        rabbitTemplate.convertAndSend(
                FeedbackConstant.FEEDBACK_EXCHANGE,
                ScheduleRulesConstant.SCHEDULE_RULES_AUTO_PUBLISH_ROUTING,
                params
        );

        // Sleep to give listener time to process message
        Thread.sleep(2000);

        // Then
        // 这里可以验证日志输出、异常处理等，确保失败场景被正确处理。
        log.info("失败场景：消息已发送到队列并处理。");
    }
}
