package com.pukka.iptv.schedule.rules.service;

import com.pukka.iptv.common.swagger.annotation.EnableCustomOpenApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * ScheduleRulesApplication 服务启动类
 */
//@Slf4j
//@ServletComponentScan
//@EnableCustomOpenApi
//@SpringBootApplication
//@EnableFeignClients("com.pukka.iptv.common.api.feign")
//@EnableScheduling
public class TestScheduleRulesApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestScheduleRulesApplication.class, args);
    }
}